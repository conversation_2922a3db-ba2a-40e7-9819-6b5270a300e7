package com.airdoc.mpd.detection.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * FileName: MaterialLibraryDto
 * Author by <PERSON><PERSON><PERSON><PERSON><PERSON>,Date on 2025/8/4
 * PS: Not easy to write code, please indicate.
 * 素材库数据传输对象
 */
@Parcelize
data class MaterialLibraryDto(
    //素材库ID
    var id: Long? = null,
    //素材库名
    var name: String? = null,
    //素材库描述
    var description: String? = null,
    //版本号
    var version: String? = null,
    //压缩包下载地址
    var zipUrl: String? = null,
    //压缩包文件大小（单位：字节）
    var size: Long? = null,
    //校验码 sha256
    var checksum: String? = null
) : Parcelable
