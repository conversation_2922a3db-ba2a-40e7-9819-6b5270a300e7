# C/C++ build system timings
generate_cxx_metadata
  [gap of 61ms]
  create-invalidation-state 177ms
  [gap of 44ms]
  write-metadata-json-to-file 23ms
generate_cxx_metadata completed in 307ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 63ms
  [gap of 30ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 118ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 15ms]
  create-invalidation-state 50ms
  [gap of 31ms]
generate_cxx_metadata completed in 96ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 31ms]
  create-invalidation-state 61ms
  [gap of 26ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 129ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 97ms
  [gap of 32ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 163ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 16ms]
  create-invalidation-state 64ms
  [gap of 28ms]
  write-metadata-json-to-file 11ms
generate_cxx_metadata completed in 119ms

