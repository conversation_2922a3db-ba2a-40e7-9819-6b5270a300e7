package com.airdoc.mpd.detection.vm

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.mpd.detection.MaterialDownloader
import com.airdoc.mpd.detection.MaterialManager
import com.airdoc.mpd.detection.bean.MaterialLibraryDto
import com.airdoc.mpd.detection.repository.MaterialRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

/**
 * FileName: MaterialViewModel
 * PS: Not easy to write code, please indicate.
 * 素材库ViewModel
 */
class MaterialViewModel(application: Application) : AndroidViewModel(application) {

    companion object {
        private val TAG = MaterialViewModel::class.java.name

        // 素材库更新成功
        const val MATERIAL_UPDATE_SUCCESS = "MATERIAL_UPDATE_SUCCESS"
        // 素材库下载成功
        const val MATERIAL_DOWNLOAD_SUCCESS = "MATERIAL_DOWNLOAD_SUCCESS"
    }

    private val materialRepository by lazy { MaterialRepository() }
    private var materialDownloader: MaterialDownloader? = null

    // 素材库列表
    val materialLibrariesLiveData = MutableLiveData<List<MaterialLibraryDto>?>()

    // 素材库更新结果
    val materialUpdateLiveData = MutableLiveData<Any?>()

    // 下载进度
    val downloadProgressLiveData = MutableLiveData<Pair<Float, Long>>()

    // 下载状态
    val downloadStatusLiveData = MutableLiveData<String>()

    init {
        // 初始化时加载本地素材库映射
        MaterialManager.loadMaterialMapping(getApplication())
    }

    /**
     * 获取有效素材库列表
     */
    fun listMaterialLibraries() {
        viewModelScope.launch {
            MutableStateFlow(materialRepository.listMaterialLibraries()).collectResponse {
                onSuccess = { it, _, _ ->
                    Logger.d(TAG, msg = "listMaterialLibraries onSuccess")
                    materialLibrariesLiveData.postValue(it)

                    // 自动下载第一个素材库（如果存在且本地不存在）
                    if (!it.isNullOrEmpty()) {
                        val firstMaterial = it.first()
                        val materialId = firstMaterial.id ?: 0L
                        if (materialId > 0 && !MaterialManager.isMaterialExists(getApplication(), materialId)) {
                            downloadMaterial(firstMaterial)
                        } else {
                            materialUpdateLiveData.postValue(MATERIAL_UPDATE_SUCCESS)
                        }
                    } else {
                        materialUpdateLiveData.postValue(MATERIAL_UPDATE_SUCCESS)
                    }
                }
                onDataEmpty = { _, _ ->
                    Logger.e(TAG, msg = "listMaterialLibraries onDataEmpty")
                    materialLibrariesLiveData.postValue(emptyList())
                    materialUpdateLiveData.postValue(MATERIAL_UPDATE_SUCCESS)
                }
                onFailed = { errorCode, errorMsg ->
                    Logger.e(TAG, msg = "listMaterialLibraries onFailed errorCode = $errorCode, errorMsg = $errorMsg")
                    materialLibrariesLiveData.postValue(null)
                    materialUpdateLiveData.postValue(Pair(errorCode, errorMsg))
                }
                onError = {
                    Logger.e(TAG, msg = "listMaterialLibraries onError = $it")
                    materialLibrariesLiveData.postValue(null)
                    materialUpdateLiveData.postValue(null)
                }
            }
        }
    }

    /**
     * 下载素材库
     */
    fun downloadMaterial(materialLibrary: MaterialLibraryDto) {
        materialDownloader = MaterialDownloader(getApplication(), object : MaterialDownloader.MaterialDownloadCallback {
            override fun onDownloadStart(materialLibrary: MaterialLibraryDto) {
                downloadStatusLiveData.postValue("开始下载: ${materialLibrary.name}")
            }

            override fun onDownloadProgress(progress: Float, total: Long) {
                downloadProgressLiveData.postValue(Pair(progress, total))
            }

            override fun onDownloadComplete(materialLibrary: MaterialLibraryDto) {
                downloadStatusLiveData.postValue("下载完成: ${materialLibrary.name}")
            }

            override fun onExtractStart(materialLibrary: MaterialLibraryDto) {
                downloadStatusLiveData.postValue("开始解压: ${materialLibrary.name}")
            }

            override fun onExtractComplete(materialLibrary: MaterialLibraryDto, extractedFileCount: Int) {
                downloadStatusLiveData.postValue("解压完成: ${materialLibrary.name}")
                materialUpdateLiveData.postValue(MATERIAL_DOWNLOAD_SUCCESS)
            }

            override fun onError(materialLibrary: MaterialLibraryDto, error: String) {
                downloadStatusLiveData.postValue("错误: $error")
                materialUpdateLiveData.postValue(Pair(-1, error))
            }
        })

        viewModelScope.launch {
            materialDownloader?.downloadAndExtractMaterial(materialLibrary)
        }
    }

    /**
     * 获取本地素材库信息
     */
    fun getLocalMaterialInfo(): List<MaterialManager.MaterialInfo> {
        return MaterialManager.getAllMaterialInfo()
    }

    /**
     * 获取素材库图片
     */
    fun getMaterialImages(materialId: Long): List<java.io.File> {
        return MaterialManager.getMaterialImages(getApplication(), materialId)
    }
}
